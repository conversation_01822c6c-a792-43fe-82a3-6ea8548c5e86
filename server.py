import http.server
import socketserver
import os

class MyHttpRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.path = '/index.html'
        elif self.path in ['/hizmetlerimiz', '/hekimlerimiz', '/blog', '/iletisim']:
            self.path = f'/pages{self.path}.html'
        elif not os.path.splitext(self.path)[1]:  # <PERSON><PERSON><PERSON> uzantı yoksa
            if os.path.exists(f'pages{self.path}.html'):  # pages dizininde mi diye kontrol et
                self.path = f'/pages{self.path}.html'
            else:
                self.path += '.html'  # değilse normal .html ekle
        
        try:
            return http.server.SimpleHTTPRequestHandler.do_GET(self)
        except FileNotFoundError:
            self.send_error(404, "File not found")

PORT = 8005
Handler = MyHttpRequestHandler

# Socket yeniden kullanımını etkinleştir
socketserver.TCPServer.allow_reuse_address = True

with socketserver.TCPServer(("", PORT), <PERSON><PERSON>) as httpd:
    print(f"Server started at http://localhost:{PORT}")
    httpd.serve_forever() 