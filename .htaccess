# PHP değerlerini ayarla (sadece PHP modülü varsa)
<IfModule mod_php.c>
    php_value memory_limit 256M
    php_value upload_max_filesize 64M
    php_value post_max_size 64M
    php_value max_execution_time 300
    php_value max_input_time 300
</IfModule>

# Tarayıcı önbellekleme
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Varsayılan önbellek süresi (1 ay)
    ExpiresDefault "access plus 1 month"
    
    # HTML ve XML dosyaları için önbellek (2 gün)
    ExpiresByType text/html "access plus 2 days"
    ExpiresByType text/xml "access plus 2 days"
    ExpiresByType application/xml "access plus 2 days"
    
    # CSS ve JavaScript dosyaları için önbellek (1 ay)
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    
    # Resi<PERSON> i<PERSON><PERSON> (1 yıl)
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # Fontlar için önbellek (1 yıl)
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType application/x-font-ttf "access plus 1 year"
    ExpiresByType application/x-font-opentype "access plus 1 year"
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
</IfModule>

# GZIP sıkıştırma
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/x-httpd-php
    AddOutputFilterByType DEFLATE application/x-httpd-fastphp
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Keep-Alive etkinleştir
<IfModule mod_headers.c>
    Header set Connection keep-alive
</IfModule>

# ETags devre dışı bırak
FileETag None

# Güvenlik başlıkları
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    Header set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # CORS ayarları
    Header set Access-Control-Allow-Origin "*"
    
    # Cache-Control başlıkları
    <FilesMatch "\.(ico|jpg|jpeg|png|gif|webp|svg|css|js)$">
        Header set Cache-Control "max-age=31536000, public"
    </FilesMatch>
    
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "max-age=7200, must-revalidate"
    </FilesMatch>
</IfModule>

RewriteEngine On

# Ana sayfa yönlendirmeleri - tüm index varyasyonları anasayfaya yönlendir
RewriteRule ^index/?$ / [R=301,L]
RewriteRule ^index\.php/?$ / [R=301,L]
RewriteRule ^index\.html/?$ / [R=301,L]
RewriteRule ^anasayfa/?$ / [R=301,L]
RewriteRule ^home/?$ / [R=301,L]

# Pages klasöründen kök dizine yönlendirme
RewriteRule ^pages/(.*)$ /$1 [R=301,L]

# Yasaklı dizinleri koru (önce güvenlik kuralları)
RewriteRule ^includes/ - [F,L]
RewriteRule ^assets/.*\.(html|php)$ - [F,L]

# Dosya uzantılarını kaldır - önce mevcut dosyaları kontrol et
# Ana dizindeki PHP dosyaları için
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{DOCUMENT_ROOT}/$1.php -f
RewriteRule ^([^/]+)/?$ $1.php [L]

# Ana dizindeki HTML dosyaları için
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{DOCUMENT_ROOT}/$1.html -f
RewriteRule ^([^/]+)/?$ $1.html [L]

# Pages klasöründeki PHP dosyaları için
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{DOCUMENT_ROOT}/pages/$1.php -f
RewriteRule ^([^/]+)/?$ pages/$1.php [L]

# Protect sensitive files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent directory listing
Options -Indexes

# Özel hata sayfaları (mutlak yol yerine göreceli yol)
ErrorDocument 404 /404.html
ErrorDocument 403 /404.html
ErrorDocument 500 /404.html
