// Telegram Bot Konfigürasyonu
const TELEGRAM_BOT_TOKEN = '**********************************************'; // Bo<PERSON><PERSON>'dan aldığınız token
const TELEGRAM_CHAT_ID = '974820258'; // Chat ID'nizi buraya yazın

// Form gönder butonu click'ini yakala
document.getElementById('submitBtn').addEventListener('click', async function(e) {
    e.preventDefault();
    
    // Form verilerini al
    const name = document.getElementById('name').value;
    const phone = document.getElementById('phone').value;
    const email = document.getElementById('email').value;
    const service = document.getElementById('service').value;
    const message = document.getElementById('message').value;
    
    // Form validation
    const currentLang = document.querySelector('[data-tr][data-en]').textContent === document.querySelector('[data-tr][data-en]').getAttribute('data-en') ? 'en' : 'tr';
    
    if (!name.trim()) {
        const message = currentLang === 'en' ? '❌ Please enter your full name.' : '❌ Lütfen adınızı ve soyadınızı girin.';
        showNotification(message, 'error');
        return;
    }
    if (!phone.trim()) {
        const message = currentLang === 'en' ? '❌ Please enter your phone number.' : '❌ Lütfen telefon numaranızı girin.';
        showNotification(message, 'error');
        return;
    }
    if (!email.trim()) {
        const message = currentLang === 'en' ? '❌ Please enter your email address.' : '❌ Lütfen e-posta adresinizi girin.';
        showNotification(message, 'error');
        return;
    }
    if (!service) {
        const message = currentLang === 'en' ? '❌ Please select a service.' : '❌ Lütfen bir hizmet seçin.';
        showNotification(message, 'error');
        return;
    }
    if (!message.trim()) {
        const message = currentLang === 'en' ? '❌ Please write your message.' : '❌ Lütfen mesajınızı yazın.';
        showNotification(message, 'error');
        return;
    }
    
    // Hizmet isimlerini Türkçe'ye çevir
    const serviceNames = {
        'implant': 'İmplant Tedavisi',
        'ortodonti': 'Ortodonti',
        'estetik': 'Estetik Diş Hekimliği',
        'endodonti': 'Endodonti',
        'pedodonti': 'Pedodonti',
        'cerrahi': 'Ağız ve Çene Cerrahisi'
    };
    
    // Telegram mesajını oluştur
    const telegramMessage = `
🦷 *YENİ İLETİŞİM FORMU*

👤 *Ad Soyad:* ${name}
📞 *Telefon:* ${phone}
📧 *E-posta:* ${email}
🏥 *Hizmet:* ${serviceNames[service] || service}
💬 *Mesaj:* ${message}

⏰ *Tarih:* ${new Date().toLocaleString('tr-TR')}
    `;
    
    try {
        // Telegram'a mesaj gönder
        const response = await fetch(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                chat_id: TELEGRAM_CHAT_ID,
                text: telegramMessage,
                parse_mode: 'Markdown'
            })
        });
        
        if (response.ok) {
            // Başarı mesajı göster
            const successMessage = currentLang === 'en' ? '✅ Your message has been sent successfully! We will get back to you as soon as possible.' : '✅ Mesajınız başarıyla gönderildi! En kısa sürede size dönüş yapacağız.';
            showNotification(successMessage, 'success');
            // Formu temizle
            document.getElementById('contactForm').reset();
        } else {
            throw new Error('Telegram API Error');
        }
    } catch (error) {
        console.error('Error:', error);
        const errorMessage = currentLang === 'en' ? '❌ An error occurred while sending the message. Please try again later.' : '❌ Mesaj gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
        showNotification(errorMessage, 'error');
    }
});

// Bildirim gösterme fonksiyonu
function showNotification(message, type) {
    // Mevcut bildirimi kaldır
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Yeni bildirim oluştur
    const notification = document.createElement('div');
    notification.className = `notification fixed top-4 right-4 z-50 px-6 py-4 rounded-lg shadow-lg max-w-md transform transition-all duration-300 translate-x-full`;
    
    if (type === 'success') {
        notification.classList.add('bg-green-500', 'text-white');
    } else {
        notification.classList.add('bg-red-500', 'text-white');
    }
    
    notification.innerHTML = `
        <div class="flex items-start gap-3">
            <div class="flex-1 text-sm">${message}</div>
            <button onclick="this.parentElement.parentElement.remove()" class="text-white/80 hover:text-white">
                <i class="ri-close-line"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animasyon için kısa gecikme
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // 5 saniye sonra otomatik kaldır
    setTimeout(() => {
        if (notification.parentElement) {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
} 